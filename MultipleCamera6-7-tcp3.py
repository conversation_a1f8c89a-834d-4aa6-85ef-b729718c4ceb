# -*- coding: utf-8 -*-
import sys
import time
import os
import threading
from queue import Queue
import numpy as np
import cv2
import open3d as o3d
import threading
import math
import socket
import os
os.environ["QT_XCB_GL_INTEGRATION"] = "none"
from collections import deque
import threading, time
from scipy.spatial import KDTree

from PyQt5.QtWidgets import *
from PyQt5.QtGui import QTextCursor, QKeySequence
from PyQt5.QtCore import QObject, pyqtSignal, QTimer, qRegisterMetaType

# 注册 QTextCursor 类型以避免信号连接错误
qRegisterMetaType(QTextCursor)
from CamOperation_class import CameraOperation
from MvCameraControl_class import *
from MvErrorDefine_const import *
from CameraParams_header import *
from PyUIMultipleCameras import Ui_MainWindow
from ultralytics import YOLO
import ctypes
_TRIGGER_LOCK   = threading.Lock()   # 防止同时多次触发
_TRIGGER_COOL   = False              # 简易冷却标志
_TRIGGER_COOL_T = 1.0                # 冷却时间 s

# {{ AURA-X: Delete - 移除错误的全局函数定义. Approval: 寸止(ID:1678886400). }}

class StereoVisionProcessor(QObject):
    # 定义信号用于更新UI
    depth_result_signal = pyqtSignal(np.ndarray, np.ndarray, np.ndarray)  # disparity, depth, depth_color

    def __init__(self):
        super().__init__()
        self.calibration_params = None
        self.rectification_maps = None
        self.Q_matrix = None
        self.calibration_available = False

        #self.yolo_model = YOLO("/opt/MVS/Samples/aarch64/yolo11_detect/data/best.pt")  # 或 "yolov11.pt"
        self.yolo_model = YOLO("/opt/MVS/Samples/aarch64/yolo11_detect/data/segmodel/best.pt")
        self.target_classes = [0]  # process_stereo_pair

        # 机器人发送队列 & 控制变量
        self.robot_tx_queue = deque()  # [(data, ip, port, delay_ms), ...]
        self.robot_tx_thread = None
        self.robot_tx_running = False
        self.robot_tx_delay_ms = 1000  # 默认 1000 ms

        # 立体匹配器
        self.stereo_matcher = self.create_stereo_matcher()

        # 图像队列用于同步
        self.left_image_queue = Queue(maxsize=2)
        self.right_image_queue = Queue(maxsize=2)

        # 处理标志
        self.processing = False
        self.process_thread = None

        # 存储最新的深度图
        self.latest_depth = None
        self.latest_disparity = None
        self.depth_lock = threading.Lock()

        # 图像保存控制参数
        self.save_input_images = True      # 是否保存原始输入图像
        # self.save_rectified_images = True  # 是否保存校正后图像
        self.save_comparison_images = True # 是否保存对比图像
        self.image_save_interval = 5       # 图像保存间隔（秒）
        self.last_save_time = 0           # 上次保存时间

        # 尝试初始化标定参数
        try:
            self.init_calibration_params()
        except Exception as e:
            print(f"Warning: Calibration initialization failed: {e}")
            print("Using simplified stereo processing without calibration")
            self.calibration_available = False

    def _robot_sender_thread(self):
        """机器人延迟发送线程：阻塞式延迟后单条发送"""
        while self.robot_tx_running:
            try:
                if self.robot_tx_queue:
                    data, ip, port, delay_ms = self.robot_tx_queue.popleft()
                    time.sleep(delay_ms / 1000.0)
                    self._send_now(data, ip, port)
                else:
                    time.sleep(0.01)
            except Exception as e:
                print("[ERROR] 机器人发送线程异常:", e)

    def _send_now(self, data, ip, port):
        """真正执行 socket 发送"""
        try:
            with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
                s.settimeout(2.0)
                s.connect((ip, port))
                s.sendall(','.join(map(str, data)).encode('utf-8'))
                print("✅ 已发送数据到机器人:", data)
        except Exception as e:
            print("❌ 发送失败:", e)

    def send_data_to_robot_delayed(self, data, ip, port, delay_ms=None):
        """
        线程安全地把数据放进延迟发送队列
        如果 delay_ms 为 None，使用 self.robot_tx_delay_ms
        """
        delay = delay_ms if delay_ms is not None else self.robot_tx_delay_ms
        print(f"[DEBUG] 加入发送队列: {data} 延迟 {delay} ms")
        self.robot_tx_queue.append((data, ip, port, delay))

    def set_robot_delay_ms(self, delay_ms: int):
        """运行时改变默认延迟"""
        self.robot_tx_delay_ms = max(0, delay_ms)

    def create_stereo_matcher(self):
        """创建立体匹配器 - 基于ceshiliti代码的方法"""
        # 使用与ceshiliti代码相同的参数设置
        stereo = cv2.StereoSGBM_create(
            minDisparity=0,
            numDisparities=96,  # 增加视差范围
            blockSize=7,  # 增加块大小
            P1=8 * 3 * 7 ** 2,
            P2=32 * 3 * 7 ** 2,
            disp12MaxDiff=1,
            uniquenessRatio=10,
            speckleWindowSize=100,
            speckleRange=32,
            preFilterCap=63,
            mode=cv2.STEREO_SGBM_MODE_SGBM_3WAY
        )
        return stereo

    def init_calibration_params(self):
        """初始化相机标定参数"""
        try:
            print("Initializing stereo calibration parameters...")

            # 尝试从JSON文件加载真实标定参数
            try:
                import json
                with open('stereo_calibration.json', 'r') as f:
                    calib_data = json.load(f)

                print("Loading calibration parameters from stereo_calibration.json")

                # 加载真实的标定参数
                self.camera_matrix_left = np.array(calib_data['camera_matrix_left'], dtype=np.float64)
                self.camera_matrix_right = np.array(calib_data['camera_matrix_right'], dtype=np.float64)

                # 畸变系数
                self.dist_coeffs_left = np.array(calib_data['dist_coeffs_left'], dtype=np.float64)
                self.dist_coeffs_right = np.array(calib_data['dist_coeffs_right'], dtype=np.float64)

                # 立体标定参数
                self.R = np.array(calib_data['R'], dtype=np.float64)
                self.T = np.array(calib_data['T'], dtype=np.float64)

                # 图像尺寸
                self.image_size = tuple(calib_data['image_size'])  # (2448, 2048)
                self.baseline_mm = calib_data['baseline_mm']

                print(f"Loaded calibration: Image size {self.image_size}, Baseline {self.baseline_mm:.2f}mm")

            except (FileNotFoundError, KeyError, json.JSONDecodeError) as e:
                print(f"Failed to load calibration file: {e}")
                print("Using provided calibration parameters...")

                # 使用您提供的真实标定参数
                self.camera_matrix_left = np.array([
                    [2349.599303017811, 0.0, 1221.0886985822297],
                    [0.0, 2347.04087849075, 1021.2297950652342],
                    [0.0, 0.0, 1.0]
                ], dtype=np.float64)

                self.camera_matrix_right = np.array([
                    [2347.7080632127045, 0.0, 1219.3168735048296],
                    [0.0, 2347.528871737054, 1010.4282529230558],
                    [0.0, 0.0, 1.0]
                ], dtype=np.float64)

                # 畸变系数 - 转换为正确的形状
                self.dist_coeffs_left = np.array([
                    [-0.0677072434895743, 0.16840134514589222, -0.00013311325437381048,
                     -0.0010946605867930416, -0.19743756744235746]
                ], dtype=np.float64).reshape(5, 1)

                self.dist_coeffs_right = np.array([
                    [-0.07691265481784593, 0.22703604995053306, 0.00015959041360151294,
                     -0.0011580170802655745, -0.3538743014783903]
                ], dtype=np.float64).reshape(5, 1)

                # 立体标定参数
                self.R = np.array([
                    [0.9998155478060632, -0.0008436404567199599, -0.01918746038540903],
                    [0.0008172502236645439, 0.9999987094440285, -0.0013831891951225166],
                    [0.019188602537281753, 0.0013672531065493177, 0.9998149469534894]
                ], dtype=np.float64)

                self.T = np.array([
                    [-100.87040766250446],
                    [0.06079718879422688],
                    [-1.3284405860235702]
                ], dtype=np.float64)

                # 图像尺寸和基线距离
                self.image_size = (2448, 2048)  # 使用您提供的真实图像尺寸
                self.baseline_mm = 100.87917323555243  # 使用您提供的真实基线距离

                print(f"Using provided calibration: Image size {self.image_size}, Baseline {self.baseline_mm:.2f}mm")

            # 尝试计算校正映射，如果失败则跳过
            try:
                self.compute_rectification_maps()
                self.calibration_available = True
                print("Calibration parameters initialized successfully")
            except Exception as calib_error:
                print(f"Calibration mapping failed: {calib_error}")
                print("Will use simplified stereo processing")
                self.calibration_available = False
                # 设置基于真实标定参数的简化Q矩阵
                # 使用真实的主点坐标和焦距
                cx = 1220.0  # 主点x坐标的平均值
                cy = 1015.0  # 主点y坐标的平均值
                fx = 2348.0  # 焦距的平均值
                baseline = 100.88  # 真实基线距离(mm)

                self.Q_matrix = np.array([
                    [1.0, 0.0, 0.0, -cx],
                    [0.0, 1.0, 0.0, -cy],
                    [0.0, 0.0, 0.0, fx],
                    [0.0, 0.0, -1.0/baseline, 0.0]
                ], dtype=np.float64)

        except Exception as e:
            print(f"Error initializing calibration parameters: {e}")
            # 设置默认值避免崩溃
            self.camera_matrix_left = None
            self.camera_matrix_right = None
            self.dist_coeffs_left = None
            self.dist_coeffs_right = None
            self.R = None
            self.T = None
            self.Q_matrix = None
            self.calibration_available = False

    def compute_rectification_maps(self):
        """计算校正映射"""
        try:
            # 检查参数是否有效
            if (self.camera_matrix_left is None or self.camera_matrix_right is None or
                self.dist_coeffs_left is None or self.dist_coeffs_right is None or
                self.R is None or self.T is None):
                print("Calibration parameters not properly initialized")
                return

            # 使用真实的图像尺寸
            image_size = getattr(self, 'image_size', (640, 480))

            # 确保所有参数都是正确的数据类型
            R1, R2, P1, P2, self.Q_matrix, _, _ = cv2.stereoRectify(
                self.camera_matrix_left.astype(np.float64),
                self.dist_coeffs_left.astype(np.float64),
                self.camera_matrix_right.astype(np.float64),
                self.dist_coeffs_right.astype(np.float64),
                image_size,
                self.R.astype(np.float64),
                self.T.astype(np.float64)
            )

            self.map1_left, self.map2_left = cv2.initUndistortRectifyMap(
                self.camera_matrix_left.astype(np.float64),
                self.dist_coeffs_left.astype(np.float64),
                R1, P1, image_size, cv2.CV_16SC2
            )

            self.map1_right, self.map2_right = cv2.initUndistortRectifyMap(
                self.camera_matrix_right.astype(np.float64),
                self.dist_coeffs_right.astype(np.float64),
                R2, P2, image_size, cv2.CV_16SC2
            )

        except Exception as e:
            print(f"Error computing rectification maps: {e}")
            # 设置为None避免后续错误
            self.map1_left = None
            self.map2_left = None
            self.map1_right = None
            self.map2_right = None
            self.Q_matrix = None

    def rectify_images(self, left_img, right_img):
        """校正图像"""
        try:
            # 如果没有标定参数，直接返回原图像
            if not self.calibration_available:
                return left_img, right_img

            # 检查映射是否有效
            if (self.map1_left is None or self.map2_left is None or
                self.map1_right is None or self.map2_right is None):
                print("Rectification maps not available, returning original images")
                return left_img, right_img

            left_rectified = cv2.remap(left_img, self.map1_left, self.map2_left, cv2.INTER_LINEAR)
            right_rectified = cv2.remap(right_img, self.map1_right, self.map2_right, cv2.INTER_LINEAR)
            return left_rectified, right_rectified
        except Exception as e:
            print(f"Error rectifying images: {e}")
            return left_img, right_img

    def compute_disparity(self, left_img, right_img):
        """计算视差图 - 基于ceshiliti代码的方法"""
        # 转换为灰度图
        if len(left_img.shape) == 3:
            left_gray = cv2.cvtColor(left_img, cv2.COLOR_BGR2GRAY)
            right_gray = cv2.cvtColor(right_img, cv2.COLOR_BGR2GRAY)
        else:
            left_gray = left_img
            right_gray = right_img

        # 校正图像
        left_rect, right_rect = self.rectify_images(left_gray, right_gray)

        # 计算视差
        disparity = self.stereo_matcher.compute(left_rect, right_rect)

        # 转换为浮点数并归一化
        disparity = disparity.astype(np.float32) / 16.0

        return disparity, left_rect, right_rect

    def disparity_to_depth(self, disparity):
        """将视差转换为深度 - 基于ceshiliti代码的方法"""
        if self.Q_matrix is None:
            return None

        # 使用Q矩阵重投影到3D
        points_3d = cv2.reprojectImageTo3D(disparity, self.Q_matrix)

        # 提取深度信息（Z坐标）
        depth = points_3d[:, :, 2]

        # 过滤无效深度值
        depth[depth <= 0] = 0
        depth[depth > 5000] = 0  # 限制最大深度为5米

        return depth

    def create_depth_colormap(self, depth):
        """创建深度图的彩色可视化 - 基于ceshiliti代码的方法"""
        # 归一化深度值到0-255
        depth_norm = cv2.normalize(depth, None, 0, 255, cv2.NORM_MINMAX)
        depth_norm = depth_norm.astype(np.uint8)

        # 应用彩色映射
        depth_color = cv2.applyColorMap(depth_norm, cv2.COLORMAP_JET)

        # 将无效区域设为黑色
        mask = depth <= 0
        depth_color[mask] = [0, 0, 0]

        return depth_color

    def compute_depth(self, disparity):
        """从视差计算深度 - 根据真实标定参数优化"""
        try:
            if self.Q_matrix is None:
                print("Q matrix not available for depth computation")
                return None, None

            # 重投影到3D
            points_3d = cv2.reprojectImageTo3D(disparity, self.Q_matrix)
            depth = points_3d[:, :, 2]

            depth[depth < 2000] = 0    # 最小深度1.5米 (考虑实际应用场景)
            depth[depth > 5000] = 0    # 最大深度8米 (根据视差精度限制)

            # 过滤异常值 (深度变化过大的点)
            depth_median = np.median(depth[depth > 0])
            if depth_median > 0:
                depth_std = np.std(depth[depth > 0])
                depth[np.abs(depth - depth_median) > 3 * depth_std] = 0

            return depth, points_3d
        except Exception as e:
            print(f"Error computing depth: {e}")
            return None, None

    def add_image_pair(self, left_img, right_img):
        """添加图像对到处理队列"""
        try:
            if not self.left_image_queue.full():
                self.left_image_queue.put(left_img, block=False)
            if not self.right_image_queue.full():
                self.right_image_queue.put(right_img, block=False)
        except:
            pass

    def start_processing(self):
        """启动处理线程"""
        if not self.processing:
            self.processing = True
            self.process_thread = threading.Thread(target=self.processing_loop)
            self.process_thread.daemon = True
            self.process_thread.start()

            # 启动机器人发送线程
            if not self.robot_tx_running:
                self.robot_tx_running = True
                self.robot_tx_thread = threading.Thread(target=self._robot_sender_thread, daemon=True)
                self.robot_tx_thread.start()

    def stop_processing(self):
        """停止处理"""
        self.processing = False
        if self.process_thread:
            self.process_thread.join()

            # 结束机器人发送线程
        self.robot_tx_running = False
        if self.robot_tx_thread and self.robot_tx_thread.is_alive():
            self.robot_tx_thread.join(timeout=1)

    def processing_loop(self):
        """处理循环"""
        while self.processing:
            try:
                if not self.left_image_queue.empty() and not self.right_image_queue.empty():
                    left_img = self.left_image_queue.get()
                    right_img = self.right_image_queue.get()
                    self.process_stereo_pair(left_img, right_img)
                else:
                    time.sleep(0.01)
            except Exception as e:
                print(f"Stereo processing error: {e}")
                time.sleep(0.1)

    def search_valid_depth_by_direction(self, depth, x, y, corner_id,
                                        search_radius=100):
        """
        按方向搜索最近有效深度点
        返回: (x_found, y_found, z_found) 或 None
        """
        h, w = depth.shape
        # 方向映射
        direction_map = {
            0: (1, 1),  # 左上
            1: (-1, 1),  # 右上
            2: (1, -1),  # 左下
            3: (-1, 1)  # 右下
        }
        dx_dir, dy_dir = direction_map.get(corner_id, (0, 0))

        best = None
        min_dist = float('inf')

        for r in range(1, search_radius + 1):
            nx = int(x + dx_dir * r)
            ny = int(y + dy_dir * r)
            if 0 <= nx < w and 0 <= ny < h:
                z = depth[ny, nx]
                if z > 0:  # 有效深度
                    best = (nx, ny, z)
                    break  # 找到最近即可
        return best

    def crop_image_to_roi(self, image, roi_x1, roi_y1, roi_x2, roi_y2):
        """裁剪图像到 ROI 区域"""
        return image[roi_y1:roi_y2, roi_x1:roi_x2]

    def adjust_boxes_to_original(self, boxes, roi_x1, roi_y1):
        """将检测框坐标从裁剪后的图像映射回原始图像"""
        adjusted_boxes = []
        for box in boxes:
            x1, y1, x2, y2 = box
            adjusted_boxes.append([x1 + roi_x1, y1 + roi_y1, x2 + roi_x1, y2 + roi_y1])
        return np.array(adjusted_boxes)

    def expand_box(self, box, expand_pixels, img_shape):
        """
        扩展识别框
        :param box: 原始识别框坐标 (x1, y1, x2, y2)
        :param expand_pixels: 扩展的像素数
        :param img_shape: 图像的形状
        :return: 扩展后的识别框坐标
        """
        x1, y1, x2, y2 = box
        new_x1 = max(0, x1 - expand_pixels)
        new_y1 = max(0, y1 - expand_pixels)
        new_x2 = min(img_shape[1], x2 + expand_pixels)
        new_y2 = min(img_shape[0], y2 + expand_pixels)
        return new_x1, new_y1, new_x2, new_y2

    def process_stereo_pair(self, left_img, right_img):
        cv2.imwrite("debug_left_before_yolo.png", left_img)

        try:
            # 1. 定义 ROI 坐标
            roi_x1, roi_y1, roi_x2, roi_y2 = 1300, 400, 2300, 2000  # 示例 ROI

            # 2. 在原始图像上绘制 ROI 边界框
            cv2.rectangle(left_img, (roi_x1, roi_y1), (roi_x2, roi_y2), (0, 0, 255), 2)  # 红色框
            cv2.putText(left_img, "ROI", (roi_x1, roi_y1 - 10), cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 0, 255), 2)

            # 2. 裁剪左图像到 ROI 区域
            cropped_left_img = self.crop_image_to_roi(left_img, roi_x1, roi_y1, roi_x2, roi_y2)

            # 3. YOLO检测整张图
            results = self.yolo_model(cropped_left_img, classes=self.target_classes)

            #print("[DEBUG] YOLO 检测结果:", results)
            #print("[DEBUG] 检测框数量:", len(results[0].boxes) if results else 0)
            if not results or len(results[0].boxes) == 0:
                print("未检测到目标")
                return

            # 4. 调整检测框坐标到原始图像
            boxes = results[0].boxes.xyxy.cpu().numpy().astype(int)
            adjusted_boxes = self.adjust_boxes_to_original(boxes, roi_x1, roi_y1)

            # 扩展识别框
            expand_pixels = 30  # 扩展30像素
            expanded_boxes = [self.expand_box(box, expand_pixels, left_img.shape) for box in adjusted_boxes]

            # 5. 用整图做立体匹配
            disparity, left_rect, right_rect = self.compute_disparity(left_img, right_img)
            #print("[DEBUG] compute_disparity 完成")
            depth, points_3d = self.compute_depth(disparity)
            #print("[DEBUG] compute_depth 完成，depth 是否为 None:", depth is None)
            if depth is None:
                print("Failed to compute depth")
                return

            # 6. 画框
            print(f"[DEBUG] 准备保存物体点云，共 {len(expanded_boxes)} 个物体")
            for idx, (x1, y1, x2, y2) in enumerate(expanded_boxes):
                print(f"[DEBUG] 保存 Object {idx} 点云")
                # self.save_object_point_cloud(points_3d, (x1, y1, x2, y2), left_img, idx)

                cv2.rectangle(left_img, (x1, y1), (x2, y2), (0, 255, 0), 2)
                cv2.putText(left_img, f"Obj{idx}", (x1, y1 - 10), cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 255, 0), 2)

                # 四个角的二维坐标
                corners_2d = [(x1, y1), (x2, y1), (x1, y2), (x2, y2)]

                # 获取四个角的深度值
                corner_depths = [depth[y, x] for x, y in corners_2d]

                # 计算四个角的三维坐标
                camera_matrix = self.camera_matrix_left  # 使用左相机的内参
                fx, fy, cx, cy = camera_matrix[0, 0], camera_matrix[1, 1], camera_matrix[0, 2], camera_matrix[1, 2]

                corners_3d = []
                for corner_idx, ((u, v), Z) in enumerate(zip(corners_2d, corner_depths)):
                    if Z > 0:
                        X = (u - cx) * Z / fx
                        Y = (v - cy) * Z / fy
                        corners_3d.append((X, Y, Z))
                        print(f"Corner {corner_idx}: 原始点 ({u}, {v}) -> ({X:.2f}, {Y:.2f}, {Z:.2f}) mm")
                    else:
                        # 方向搜索
                        found = self.search_valid_depth_by_direction(depth, u, v, corner_idx)
                        if found is not None:
                            nx, ny, nz = found
                            X = (nx - cx) * nz / fx
                            Y = (ny - cy) * nz / fy
                            corners_3d.append((X, Y, nz))
                            print(f"Corner {corner_idx}: 方向搜索点 ({nx}, {ny}) -> ({X:.2f}, {Y:.2f}, {nz:.2f}) mm")
                            # 在图像上标记
                            cv2.circle(left_img, (nx, ny), 6, (0, 0, 255), -1)
                            cv2.putText(left_img, f"C{corner_idx}", (nx + 8, ny - 8),
                                        cv2.FONT_HERSHEY_SIMPLEX, 0.6, (0, 0, 255), 2)
                        else:
                            corners_3d.append((None, None, None))
                            print(f"Corner {corner_idx}: 未找到有效深度点")

                # 精确平移：XY 同时偏移
                delta_map = {
                    0: (-10.0, -20.0),  # 左上
                    1: (40.0, -20.0),  # 右上
                    2: (-10.0, 0.0),  # 左下
                    3: (40.0, 0.0),  # 右下
                }
                for i, c in enumerate(corners_3d):
                    if c is not None and None not in c:
                        dx, dy = delta_map[i]
                        corners_3d[i] = (c[0] + dx, c[1] + dy, c[2])

                # {{ AURA-X: Modify - 添加调试信息，帮助跟踪程序执行进度. Approval: 寸止(ID:1678886400). }}
                # ✅ 计算完 corners_3d（点1）后，统一滤波整个点云
                print("[DEBUG] 开始点云滤波处理...")
                save_dir = "refined_corners"
                os.makedirs(save_dir, exist_ok=True)
                timestamp = int(time.time() * 1000)

                height, width = points_3d.shape[:2]
                valid_mask = (points_3d[:, :, 2] >= 2000) & (points_3d[:, :, 2] <= 5000)
                valid_points = points_3d[valid_mask]
                valid_colors = cv2.cvtColor(left_img, cv2.COLOR_BGR2RGB)[valid_mask]
                print(f"[DEBUG] 有效点数: {len(valid_points)}")

                if len(valid_points) > 0:
                    filtered_pcd = self._save_ply(valid_points, valid_colors, save_dir, timestamp)

                    # {{ AURA-X: Modify - 添加防护性检查，避免None值导致崩溃. Approval: 寸止(ID:1678886400). }}
                    # ✅ 在滤波后的点云中查找每个角点的最近点（点2）
                    refined_corners = []
                    if filtered_pcd is not None:
                        for idx, corner_3d in enumerate(corners_3d):
                            if corner_3d is not None and None not in corner_3d:
                                nearest = self.find_nearest_valid_point_from_filtered_cloud(corner_3d, filtered_pcd)
                                refined_corners.append(nearest)
                                print(f"[REFINED] 角点{idx} 原始: {corner_3d} → 最近: {nearest}")
                            else:
                                refined_corners.append(None)
                    else:
                        print("[WARNING] 点云滤波失败，使用原始角点")
                        refined_corners = corners_3d.copy()

                    def calculate_rect_metrics(corners):
                        """
                        输入：4 个 3D 角点 [(x,y,z), ...]
                        输出：长度、宽度、中心点
                        """
                        if len(corners) < 4 or any(c is None for c in corners):
                            return None

                        # 假设顺序：左上、右上、左下、右下
                        tl, tr, bl, br = corners

                        # 计算两条边的长度
                        length = math.dist(tl, tr)  # 上边
                        width = math.dist(tl, bl)  # 左边

                        # 中心点 = 四个点的平均
                        center = (
                            (tl[0] + tr[0] + bl[0] + br[0]) / 4,
                            (tl[1] + tr[1] + bl[1] + br[1]) / 4,
                            (tl[2] + tr[2] + bl[2] + br[2]) / 4,
                        )

                        return {"length": length, "width": width, "center": center}

                    metrics = calculate_rect_metrics(refined_corners)
                    if metrics:
                        print("�� 矩形参数：")
                        print(f"   长度: {metrics['length']:.2f} mm")
                        print(f"   宽度: {metrics['width']:.2f} mm")
                        print(
                            f"   中心点: ({metrics['center'][0]:.2f}, {metrics['center'][1]:.2f}, {metrics['center'][2]:.2f}) mm")

                        # # ✅ 发送数据到机器人
                        # {{ AURA-X: Modify - 修复方法调用，使用self实例. Approval: 寸止(ID:1678886400). }}
                        self.send_data_to_robot_delayed(
                            [512, 1, 1, round(metrics['length']), round(metrics['width']), 200,
                             round(metrics['center'][0]), round(metrics['center'][1]), round(metrics['center'][2])],
                            '192.168.1.103',
                            60000,
                            delay_ms=1500  # 可动态改
                        )

                        # ✅ 可选：在图像上画出中心点
                        cx_2d = int((x1 + x2) / 2)
                        cy_2d = int((y1 + y2) / 2)
                        cv2.circle(left_img, (cx_2d, cy_2d), 8, (0, 255, 255), -1)
                        cv2.putText(left_img, f"Ctr", (cx_2d + 10, cy_2d - 10),
                                    cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 255, 255), 2)
                    # 收集有效角点
                    valid_corners = [c for c in refined_corners if c is not None]

            # 7. 发射信号
            self.depth_result_signal.emit(disparity, depth, left_img)
            # ✅ 保存带框的检测结果图
            save_dir_det = "yolo_detected"
            os.makedirs(save_dir_det, exist_ok=True)
            det_path = os.path.join(save_dir_det, f"yolo_detected_{int(time.time() * 1000)}.png")
            cv2.imwrite(det_path, left_img)
            print(f"[✅ YOLO检测结果已保存] {det_path}")

            with self.depth_lock:
                # 保存最新的视差/深度/点云，供触发后保存使用
                self.latest_disparity = disparity  # 保存视差
                self.latest_depth = depth          # 保存深度
                self.latest_points_3d = points_3d  # 保存点云

            # ✅ 保存角点点云（在滤波点云中查找的最近点）
            save_dir_corners = "corner_pointclouds"
            os.makedirs(save_dir_corners, exist_ok=True)

            # 收集有效角点
            valid_corners = [c for c in refined_corners if c is not None]
            if len(valid_corners) > 0:
                corner_points = np.array(valid_corners)
                # 为每个角点分配不同颜色
                corner_colors = np.array([
                    [255, 0, 0],  # 红色
                    [0, 255, 0],  # 绿色
                    [255, 255, 255],  # 蓝色
                    [255, 255, 0]  # 黄色
                ])[:len(corner_points)]

        except Exception as e:
            # print(f"立体处理错误: {e}")
            print("[ERROR] process_stereo_pair 异常:", e)
            import traceback
            traceback.print_exc()

    def set_image_save_settings(self, save_input=True, save_rectified=True, save_comparison=True, interval=5):
        """设置图像保存参数
        Args:
            save_input: 是否保存原始输入图像
            save_rectified: 是否保存校正后图像
            save_comparison: 是否保存对比图像
            interval: 保存间隔（秒）
        """
        self.save_input_images = save_input
        # self.save_rectified_images = save_rectified
        self.save_comparison_images = save_comparison
        self.image_save_interval = interval
        print(f"图像保存设置已更新: 输入图像={save_input}, 校正图像={save_rectified}, 对比图像={save_comparison}, 间隔={interval}秒")

    def force_save_current_pair(self, left_img, right_img):
        """强制保存当前图像对（忽略时间间隔限制）"""
        try:
            print("强制保存当前立体图像对...")

        except Exception as e:
            print(f"强制保存图像对错误: {e}")


    def _save_ply(self, points, colors, save_dir, timestamp):
        """强制使用滤波保存点云"""
        try:
            if len(points) == 0:
                print("[WARNING] 无有效点，跳过保存")
                return None

            # {{ AURA-X: Modify - 修复缩进错误，确保点云处理代码正常执行. Approval: 寸止(ID:1678886400). }}
            # 构造 Open3D 点云
            pcd = o3d.geometry.PointCloud()
            pcd.points = o3d.utility.Vector3dVector(points)
            pcd.colors = o3d.utility.Vector3dVector(colors / 255.0)

            # 1. 统计滤波去噪
            cl, ind = pcd.remove_statistical_outlier(nb_neighbors=20, std_ratio=2.0)
            pcd = pcd.select_by_index(ind)

            # 2. 结构保留（可选，默认开启）
            keep_frame = True
            if keep_frame:
                pts = np.asarray(pcd.points)
                kdtree = KDTree(pts)
                distances, _ = kdtree.query(pts, k=10)
                avg_distances = np.mean(distances, axis=1)
                threshold = np.percentile(avg_distances, 30)
                frame_mask = avg_distances < threshold
                frame_pcd = pcd.select_by_index(np.where(frame_mask)[0])

                # 3. DBSCAN 聚类保留最大簇
                labels = np.array(frame_pcd.cluster_dbscan(eps=20, min_points=10, print_progress=False))
                if len(labels) > 0 and labels.max() >= 0:
                    largest_cluster_idx = np.where(labels == np.argmax(np.bincount(labels[labels >= 0])))[0]
                    filtered_pcd = frame_pcd.select_by_index(largest_cluster_idx)
                else:
                    filtered_pcd = frame_pcd
            else:
                filtered_pcd = pcd

            # 4. 体素降采样
            filtered_pcd = filtered_pcd.voxel_down_sample(voxel_size=5)

            # 5. 保存最终滤波结果
            filename = f"filtered_pointcloud_{timestamp}.ply"
            filepath = os.path.join(save_dir, filename)
            o3d.io.write_point_cloud(filepath, filtered_pcd)
            print(f"[✅ 滤波点云已保存] {filepath} | 点数: {len(filtered_pcd.points)}")

            return filtered_pcd  # ✅ 加这一行

        except Exception as e:
            print(f"[ERROR] 点云滤波保存失败: {e}")
            # {{ AURA-X: Modify - 增强错误处理，返回None而不是崩溃. Approval: 寸止(ID:1678886400). }}
            import traceback
            traceback.print_exc()
            return None

    def find_nearest_valid_point_from_filtered_cloud(self, corner_3d, filtered_pcd):
        """
        在过滤后的点云中，找到离某个角点最近的点的3D坐标
        :param corner_3d: tuple (X, Y, Z) 原始角点的3D坐标
        :param filtered_pcd: open3d.geometry.PointCloud 已滤波的点云
        :return: tuple (X, Y, Z) 最近的点的3D坐标
        """
        try:
            points = np.asarray(filtered_pcd.points)
            if len(points) == 0:
                print("⚠️ 过滤后的点云为空")
                return None

            kdtree = KDTree(points)
            distance, index = kdtree.query(corner_3d)
            nearest_point = points[index]
            return tuple(nearest_point)
        except Exception as e:
            print(f"[ERROR] 查找最近点失败: {e}")
            return None

# 将返回的错误码转换为十六进制显示
def ToHexStr(num):
    """将错误码转换为十六进制字符串，处理None值"""
    if num is None:
        return "None"

    chaDic = {10: 'a', 11: 'b', 12: 'c', 13: 'd', 14: 'e', 15: 'f'}
    hexStr = ""
    if num < 0:
        num = num + 2 ** 32
    while num >= 16:
        digit = num % 16
        hexStr = chaDic.get(digit, str(digit)) + hexStr
        num //= 16
    hexStr = chaDic.get(num, str(num)) + hexStr
    return hexStr

# Decoding Characters
def decoding_char(c_ubyte_value):
    c_char_p_value = ctypes.cast(c_ubyte_value, ctypes.c_char_p)
    try:
        decode_str = c_char_p_value.value.decode('gbk')  # Chinese characters
    except UnicodeDecodeError:
        decode_str = str(c_char_p_value.value)
    return decode_str

if __name__ == "__main__":

    global deviceList
    deviceList = MV_CC_DEVICE_INFO_LIST()

    global cam_checked_list
    cam_checked_list = []

    global obj_cam_operation
    obj_cam_operation = []

    global win_display_handles
    win_display_handles = []

    global valid_number
    valid_number = 0

    global b_is_open
    b_is_open = False

    global b_is_grab
    b_is_grab = False

    global b_is_trigger
    b_is_trigger = False

    global b_is_software_trigger
    b_is_software_trigger = False

    global b_is_hardware_trigger
    b_is_hardware_trigger = False

    global hardware_trigger_line
    hardware_trigger_line = "Line0"  # 默认硬件触发线

    global hardware_trigger_activation
    hardware_trigger_activation = "RisingEdge"  # 默认上升沿触发

    # 立体视觉相关变量
    global stereo_processor
    stereo_processor = None

    global stereo_enabled
    stereo_enabled = False

    global stereo_thread
    stereo_thread = None

    global stereo_running
    stereo_running = False

    #触发后立体匹配相关变量
    global trigger_stereo_match
    trigger_stereo_match = False

    global last_trigger_images
    last_trigger_images = {"left": None, "right": None}

    _trigger_processed = False   # 全局标志，防止重复处理
    _trigger_cooldown = False  # 全局标志，防止快速连续触发
    _trigger_cooldown_time = 1.0  # 冷却时间，单位为秒
    #
    # 自动保存相关变量
    global auto_save_enabled
    auto_save_enabled = True
    #
    global auto_save_interval
    auto_save_interval = 30  # 30秒自动保存一次

    global auto_save_timer
    auto_save_timer = None

    # 全局变量
    global last_hardware_trigger_time
    last_hardware_trigger_time = 0

    global hardware_trigger_interval
    hardware_trigger_interval = 5.0  # 防抖时间间隔，单位为秒

    global hardware_trigger_enabled
    hardware_trigger_enabled = True

    # ch:初始化SDK | en: initialize SDK
    MvCamera.MV_CC_Initialize()

    # print info in ui
    def print_text(str_info):
        ui.textEdit.append(str_info)  # 使用 append 代替手动操作光标

    # 初始化立体视觉
    def init_stereo_vision():
        global stereo_processor
        try:
            print_text("正在初始化立体视觉系统...")
            stereo_processor = StereoVisionProcessor()

            # 检查立体视觉处理器是否正确初始化
            if (stereo_processor.camera_matrix_left is None or
                stereo_processor.camera_matrix_right is None):
                print_text("立体视觉初始化失败: 标定参数无效")
                stereo_processor = None
                return False

            # 连接信号
            stereo_processor.depth_result_signal.connect(update_depth_display)

            # 设置默认的图像保存参数
            stereo_processor.set_image_save_settings(
                save_input=True,      # 保存原始输入图像
                save_rectified=True,  # 保存校正后图像
                save_comparison=True, # 保存对比图像
                interval=10           # 每10秒保存一次
            )

            print_text("立体视觉系统初始化完成")
            print_text("图像保存设置: 输入图像=开启, 校正图像=开启, 保存间隔=10秒")
            return True
        except Exception as e:
            print_text(f"立体视觉初始化失败: {e}")
            print_text("将使用简化的立体视觉模式")
            stereo_processor = None
            return False

    def update_depth_display(disparity, depth, depth_color):
        try:
            # 禁用 OpenGL 显示以避免错误
            # if depth_color is not None and len(depth_color.shape) == 3:
            #     cv2.imshow("YOLO Detection", depth_color)
            #     cv2.waitKey(1)
            print_text("深度显示已更新（显示功能已禁用以避免OpenGL错误）")
        except Exception as e:
            print_text(f"显示错误: {e}")
        """更新深度显示"""

        try:
            print_text(f"深度图更新: 尺寸 {depth.shape}, 深度范围 {depth.min():.1f}-{depth.max():.1f}mm")
        except Exception as e:
            print_text(f"深度显示更新错误: {e}")

    def set_stereo_image_save_settings(save_input=True, save_rectified=True, interval=5):
        """设置立体图像保存参数"""
        global stereo_processor
        if stereo_processor is not None:
            stereo_processor.set_image_save_settings(save_input, save_rectified, True, interval)
            print_text(f"立体图像保存设置已更新: 输入图像={save_input}, 校正图像={save_rectified}, 间隔={interval}秒")
        else:
            print_text("立体视觉处理器未初始化")

    def force_save_stereo_images():
        print("[DEBUG] force_save_stereo_images() 被调用了")  # ✅ 加这一行
        """强制保存当前立体图像对"""
        global stereo_processor, obj_cam_operation

        if stereo_processor is None:
            print_text("立体视觉处理器未初始化")
            return

        try:
            # 获取当前左右相机图像
            left_image = get_camera_image(0)
            right_image = get_camera_image(1)

            if left_image is not None and right_image is not None:
                # 禁用 OpenGL 显示以避免错误
                # cv2.imshow("Left Image Preview", left_image)
                # cv2.waitKey(1)  # 保持窗口刷新，1ms 延迟
                print_text("左图像预览已更新（显示功能已禁用以避免OpenGL错误）")

                # ✅ 保存 left_img 到文件夹
                save_dir = "debug_images"
                os.makedirs(save_dir, exist_ok=True)
                timestamp = int(time.time() * 1000)
                save_path = os.path.join(save_dir, f"left_img_{timestamp}.png")
                cv2.imwrite(save_path, left_image)
                print_text(f"Left image saved: {save_path}")

                stereo_processor.force_save_current_pair(left_image, right_image)
                print_text("已强制保存当前立体图像对")
            else:
                print_text("无法获取有效的立体图像对")

        except Exception as e:
            print_text(f"强制保存立体图像错误: {e}")

    # def display_latest_stereo_images():
    #     """显示最新保存的立体图像（用于调试和验证）"""
    #     try:
    #         import glob
    #
    #         # 查找最新的输入图像
    #         input_dir = "stereo_input_images"
    #         if os.path.exists(input_dir):
    #             input_files = glob.glob(os.path.join(input_dir, "stereo_comparison_input_*.png"))
    #             if input_files:
    #                 latest_input = max(input_files, key=os.path.getctime)
    #                 print_text(f"最新输入图像对比图: {os.path.basename(latest_input)}")
    #
    #         # 查找最新的校正图像
    #         rectified_dir = "stereo_rectified_images"
    #         if os.path.exists(rectified_dir):
    #             rectified_files = glob.glob(os.path.join(rectified_dir, "stereo_comparison_rectified_*.png"))
    #             if rectified_files:
    #                 latest_rectified = max(rectified_files, key=os.path.getctime)
    #                 print_text(f"最新校正图像对比图: {os.path.basename(latest_rectified)}")

        except Exception as e:
            print_text(f"显示最新立体图像错误: {e}")

    def get_camera_image(camera_index):
        """获取指定相机的当前图像 - 修复缓冲区问题"""
        global obj_cam_operation
        try:
            if (camera_index < len(obj_cam_operation) and
                obj_cam_operation[camera_index] != 0 and
                obj_cam_operation[camera_index].b_start_grabbing):

                # 方法1：尝试使用实时图像缓冲区
                stOutFrame = MV_FRAME_OUT()
                ret = obj_cam_operation[camera_index].obj_cam.MV_CC_GetImageBuffer(stOutFrame, 1000)
                if ret == 0:
                    try:
                        # 检查缓冲区大小
                        expected_size = stOutFrame.stFrameInfo.nFrameLen
                        if expected_size > 0:
                            # 转换为OpenCV格式
                            arr = np.frombuffer(stOutFrame.pBufAddr, dtype=np.uint8, count=expected_size)
                            width = stOutFrame.stFrameInfo.nWidth
                            height = stOutFrame.stFrameInfo.nHeight

                            # 根据像素格式转换
                            if stOutFrame.stFrameInfo.enPixelType == PixelType_Gvsp_Mono8:
                                if len(arr) >= width * height:
                                    gray = arr[:width * height].reshape(height, width).copy()
                                    image = cv2.cvtColor(gray, cv2.COLOR_GRAY2BGR)
                                    # 释放图像缓冲区
                                    obj_cam_operation[camera_index].obj_cam.MV_CC_FreeImageBuffer(stOutFrame)
                                    return image
                    except Exception as e:
                        print_text(f"实时缓冲区处理错误: {e}")
                    finally:
                        # 确保释放缓冲区
                        obj_cam_operation[camera_index].obj_cam.MV_CC_FreeImageBuffer(stOutFrame)

                # 方法2：回退到保存的图像缓冲区
                if (obj_cam_operation[camera_index].buf_save_image is not None and
                    hasattr(obj_cam_operation[camera_index], 'st_frame_info')):

                    frame_info = obj_cam_operation[camera_index].st_frame_info
                    if frame_info.nHeight > 0 and frame_info.nWidth > 0:
                        try:
                            # 计算期望的缓冲区大小
                            expected_size = frame_info.nWidth * frame_info.nHeight
                            image_data = np.frombuffer(
                                obj_cam_operation[camera_index].buf_save_image,
                                dtype=np.uint8,
                                count=min(len(obj_cam_operation[camera_index].buf_save_image), expected_size)
                            )

                            if len(image_data) >= expected_size:
                                image = image_data[:expected_size].reshape((frame_info.nHeight, frame_info.nWidth))
                                return cv2.cvtColor(image, cv2.COLOR_GRAY2BGR)
                        except Exception as e:
                            print_text(f"保存缓冲区处理错误: {e}")

            return None
        except Exception as e:
            print_text(f"获取相机{camera_index}图像失败: {e}")
            return None

    def start_stereo_vision():
        """启动立体视觉"""
        global stereo_enabled, stereo_processor, stereo_thread, stereo_running

        if not b_is_grab:
            print_text("请先启动相机采集")
            return

        # 检查是否至少选择了两个相机
        selected_count = sum(1 for i in range(4) if i < len(obj_cam_operation) and obj_cam_operation[i] != 0)
        if selected_count < 2:
            print_text("立体视觉需要至少两个相机")
            return

        if stereo_processor is None:
            if not init_stereo_vision():
                return

        # 启动立体处理
        stereo_processor.start_processing()

        # {{ AURA-X: Modify - 启动图像获取线程. Approval: 寸止(ID:1678886400). }}
        # 启动图像获取线程
        stereo_enabled = True
        stereo_running = True
        stereo_thread = threading.Thread(target=stereo_image_acquisition_loop, daemon=True)
        stereo_thread.start()

        print_text("立体视觉已启动")
        print_text("立体图像将自动保存到以下目录:")
        print_text("  - 原始图像: stereo_input_images/")
        print_text("  - 校正图像: stereo_rectified_images/")
        print_text("  - 深度图像: depth_images/")
        print_text("  - 点云数据: point_clouds/ (PLY格式)")
        print_text("可调用 force_save_stereo_images() 强制保存当前图像对")

    def stop_stereo_vision():
        """停止立体视觉"""
        global stereo_enabled, stereo_processor, stereo_running

        stereo_enabled = False
        stereo_running = False

        if stereo_processor:
            stereo_processor.stop_processing()

        print_text("立体视觉已停止")

    def stereo_image_acquisition_loop():
        global stereo_running, stereo_processor, _TRIGGER_LOCK, _TRIGGER_COOL

        while stereo_running:
            try:
                # ---------- 硬件触发模式 ----------
                if b_is_hardware_trigger:
                    # 硬件触发模式下，减少检测频率避免OpenGL冲突
                    check_hardware_trigger_images()
                    time.sleep(0.5)  # 增加到500ms间隔，减少冲突
                    continue

                # ---------- 连续/软触发模式 ----------
                left_img = get_camera_image(0)
                right_img = get_camera_image(1)

                if left_img is None or right_img is None:
                    time.sleep(0.01)
                    continue

                if stereo_processor is not None:
                    stereo_processor.add_image_pair(left_img, right_img)

                time.sleep(0.033)

            except Exception as e:
                print_text(f"立体图像获取错误: {e}")
                time.sleep(0.1)

    # {{ AURA-X: Add - 添加硬件触发检测函数. Approval: 寸止(ID:1678886400). }}
    def check_hardware_trigger_images():
        """检查硬件触发图像 - 简化版本避免OpenGL冲突"""
        global obj_cam_operation, _TRIGGER_COOL, last_hardware_trigger_time

        try:
            current_time = time.time()

            # 防抖：检查距离上次触发的时间间隔
            if current_time - last_hardware_trigger_time < 1.0:  # 增加到1秒间隔
                return

            # 简化检查：只要有硬件触发模式且相机在运行就处理
            if _TRIGGER_COOL:  # 冷却期内直接丢弃
                return

            # 检查是否有相机在硬件触发模式下运行
            cameras_running = 0
            for i in range(2):  # 只检查左右相机
                if (i < len(obj_cam_operation) and obj_cam_operation[i] != 0 and
                    obj_cam_operation[i].b_start_grabbing):
                    cameras_running += 1

            if cameras_running >= 2:
                _TRIGGER_COOL = True
                last_hardware_trigger_time = current_time
                print_text("检测到硬件触发信号")

                # 使用QTimer在主线程中执行处理，避免OpenGL冲突
                QTimer.singleShot(100, handle_hardware_trigger_in_main_thread)

                # 设置冷却定时器
                QTimer.singleShot(2000, lambda: setattr(sys.modules[__name__], '_TRIGGER_COOL', False))

        except Exception as e:
            # 避免在子线程中直接调用print_text
            print(f"硬件触发检查错误: {e}")

    def handle_hardware_trigger_in_main_thread():
        """在主线程中处理硬件触发 - 简化版本避免OpenGL冲突"""
        try:
            print_text("检测到硬件触发，开始处理...")
            # 直接调用立体匹配，避免复杂的触发处理
            process_single_stereo_pair_simple()
        except Exception as e:
            print_text(f"硬件触发处理错误: {e}")

    def process_single_stereo_pair_simple():
        """简化的立体匹配处理，避免OpenGL冲突 - 仅保存图像"""
        global last_trigger_images

        try:
            # 获取左右图像
            left_image = get_camera_image(0)
            right_image = get_camera_image(1)

            if left_image is None or right_image is None:
                print_text("未能获取完整的立体图像对")
                return

            # 保存用于调试
            last_trigger_images["left"] = left_image
            last_trigger_images["right"] = right_image

            # 直接保存图像，避免复杂的立体匹配处理
            save_trigger_images_simple(left_image, right_image)

            print_text("硬件触发图像已保存")
        except Exception as e:
            print_text(f"硬件触发图像保存错误: {e}")

    def save_trigger_images_simple(left_img, right_img):
        """简单保存触发图像，避免OpenGL操作"""
        try:
            import cv2
            import os
            import time

            out_dir = "hardware_trigger_images"
            if not os.path.exists(out_dir):
                os.makedirs(out_dir)

            ts = int(time.time() * 1000)
            cv2.imwrite(os.path.join(out_dir, f"left_{ts}.png"), left_img)
            cv2.imwrite(os.path.join(out_dir, f"right_{ts}.png"), right_img)

            print_text(f"硬件触发图像已保存到 {out_dir}")
        except Exception as e:
            print_text(f"保存硬件触发图像失败: {e}")

    # {{ AURA-X: Add - 添加硬件触发处理函数. Approval: 寸止(ID:1678886400). }}
    def handle_hardware_trigger():
        """处理硬件触发事件"""
        global stereo_processor, _TRIGGER_LOCK, _TRIGGER_COOL

        # 防抖处理
        with _TRIGGER_LOCK:
            if _TRIGGER_COOL:  # 冷却期内直接丢弃
                print_text("硬件触发冷却中，跳过本次触发")
                return
            _TRIGGER_COOL = True  # 立即上锁

        try:
            print_text("收到硬件触发信号，开始处理...")

            # 获取触发时的图像
            left_img = get_camera_image(0)
            right_img = get_camera_image(1)

            if left_img is None or right_img is None:
                print_text("硬件触发时无法获取有效图像")
                return

            # 处理立体匹配
            if stereo_processor is not None:
                stereo_processor.process_stereo_pair(left_img, right_img)
                print_text("硬件触发处理完成")
            else:
                print_text("立体视觉处理器未初始化")

        except Exception as e:
            print_text(f"硬件触发处理错误: {e}")
        finally:
            # 冷却定时器
            threading.Timer(_TRIGGER_COOL_T,
                            lambda: setattr(sys.modules[__name__], '_TRIGGER_COOL', False)).start()

    # ch:枚举相机 | en:enum devices
    def enum_devices():
        global deviceList
        global valid_number
        deviceList = MV_CC_DEVICE_INFO_LIST()
        n_layer_type = (MV_GIGE_DEVICE | MV_USB_DEVICE
                        | MV_GENTL_GIGE_DEVICE | MV_GENTL_CAMERALINK_DEVICE
                        | MV_GENTL_CXP_DEVICE | MV_GENTL_XOF_DEVICE)
        ret = MvCamera.MV_CC_EnumDevicesEx2(n_layer_type, deviceList, '', SortMethod_SerialNumber)
        if ret != 0:
            str_error = "Enum devices fail! ret = :" + ToHexStr(ret)
            QMessageBox.warning(mainWindow, "Error", str_error, QMessageBox.Ok)
            return ret

        if deviceList.nDeviceNum == 0:
            QMessageBox.warning(mainWindow, "Info", "Find no device", QMessageBox.Ok)
            return ret
        print_text("Find %d devices!" % deviceList.nDeviceNum)

        valid_number = 0
        for i in range(0, 4):
            if (i < deviceList.nDeviceNum) is True:
                serial_number = ""
                model_name = ""
                mvcc_dev_info = cast(deviceList.pDeviceInfo[i], POINTER(MV_CC_DEVICE_INFO)).contents
                if mvcc_dev_info.nTLayerType == MV_GIGE_DEVICE or mvcc_dev_info.nTLayerType == MV_GENTL_GIGE_DEVICE:
                    print("\ngige device: [%d]" % i)
                    user_defined_name = decoding_char(mvcc_dev_info.SpecialInfo.stGigEInfo.chUserDefinedName)
                    model_name = decoding_char(mvcc_dev_info.SpecialInfo.stGigEInfo.chModelName)
                    print("device user define name: " + user_defined_name)
                    print("device model name: " + model_name)

                    nip1 = ((mvcc_dev_info.SpecialInfo.stGigEInfo.nCurrentIp & 0xff000000) >> 24)
                    nip2 = ((mvcc_dev_info.SpecialInfo.stGigEInfo.nCurrentIp & 0x00ff0000) >> 16)
                    nip3 = ((mvcc_dev_info.SpecialInfo.stGigEInfo.nCurrentIp & 0x0000ff00) >> 8)
                    nip4 = (mvcc_dev_info.SpecialInfo.stGigEInfo.nCurrentIp & 0x000000ff)
                    print("current ip: %d.%d.%d.%d " % (nip1, nip2, nip3, nip4))

                    for per in mvcc_dev_info.SpecialInfo.stGigEInfo.chSerialNumber:
                        if per == 0:
                            break
                        serial_number = serial_number + chr(per)

                elif mvcc_dev_info.nTLayerType == MV_USB_DEVICE:
                    print("\nu3v device: [%d]" % i)
                    user_defined_name = decoding_char(mvcc_dev_info.SpecialInfo.stUsb3VInfo.chUserDefinedName)
                    model_name = decoding_char(mvcc_dev_info.SpecialInfo.stUsb3VInfo.chModelName)
                    print("device user define name: " + user_defined_name)
                    print("device model name: " + model_name)

                    for per in mvcc_dev_info.SpecialInfo.stUsb3VInfo.chSerialNumber:
                        if per == 0:
                            break
                        serial_number = serial_number + chr(per)
                    print("user serial number: " + serial_number)
                elif mvcc_dev_info.nTLayerType == MV_GENTL_CAMERALINK_DEVICE:
                    print("\nCML device: [%d]" % i)
                    user_defined_name = decoding_char(mvcc_dev_info.SpecialInfo.stCMLInfo.chUserDefinedName)
                    model_name = decoding_char(mvcc_dev_info.SpecialInfo.stCMLInfo.chModelName)
                    print("device user define name: " + user_defined_name)
                    print("device model name: " + model_name)

                    for per in mvcc_dev_info.SpecialInfo.stCMLInfo.chSerialNumber:
                        if per == 0:
                            break
                        serial_number = serial_number + chr(per)
                    print("user serial number: " + serial_number)
                elif mvcc_dev_info.nTLayerType == MV_GENTL_CXP_DEVICE:
                    print("\nCXP device: [%d]" % i)
                    user_defined_name = decoding_char(mvcc_dev_info.SpecialInfo.stCXPInfo.chUserDefinedName)
                    model_name = decoding_char(mvcc_dev_info.SpecialInfo.stCXPInfo.chModelName)
                    print("device user define name: " + user_defined_name)
                    print("device model name: " + model_name)

                    for per in mvcc_dev_info.SpecialInfo.stCXPInfo.chSerialNumber:
                        if per == 0:
                            break
                        serial_number = serial_number + chr(per)
                    print("user serial number: " + serial_number)
                elif mvcc_dev_info.nTLayerType == MV_GENTL_XOF_DEVICE:
                    print("\nXoF device: [%d]" % i)
                    user_defined_name = decoding_char(mvcc_dev_info.SpecialInfo.stXoFInfo.chUserDefinedName)
                    model_name = decoding_char(mvcc_dev_info.SpecialInfo.stXoFInfo.chModelName)
                    print("device user define name: " + user_defined_name)
                    print("device model name: " + model_name)

                    for per in mvcc_dev_info.SpecialInfo.stXoFInfo.chSerialNumber:
                        if per == 0:
                            break
                        serial_number = serial_number + chr(per)
                    print("user serial number: " + serial_number)

                button_by_id = cam_button_group.button(i)
                button_by_id.setText("(" + serial_number + ")" + model_name)
                button_by_id.setEnabled(True)
                valid_number = valid_number + 1
            else:
                button_by_id = cam_button_group.button(i)
                button_by_id.setEnabled(False)

    def cam_check_box_clicked():
        global cam_checked_list
        cam_checked_list = []
        for i in range(0, 4):
            button = cam_button_group.button(i)
            if button.isChecked() is True:
                cam_checked_list.append(True)
            else:
                cam_checked_list.append(False)

    def enable_ui_controls():
        global b_is_open
        global b_is_grab
        global b_is_trigger
        global b_is_software_trigger
        global b_is_hardware_trigger
        ui.pushButton_enum.setEnabled(not b_is_open)
        ui.pushButton_open.setEnabled(not b_is_open)
        ui.pushButton_close.setEnabled(b_is_open)
        result1 = False if b_is_grab else b_is_open
        result2 = b_is_open if b_is_grab else False
        ui.pushButton_startGrab.setEnabled(result1)
        ui.pushButton_stopGrab.setEnabled(result2)
        ui.pushButton_saveImg.setEnabled(result2)
        ui.radioButton_continuous.setEnabled(b_is_open)
        ui.radioButton_trigger.setEnabled(b_is_open)
        ui.pushButton_setParams.setEnabled(b_is_open)
        ui.lineEdit_gain.setEnabled(b_is_open)
        ui.lineEdit_frameRate.setEnabled(b_is_open)
        ui.lineEdit_exposureTime.setEnabled(b_is_open)
        result3 = b_is_open if b_is_trigger else False
        ui.pushButton_triggerOnce.setEnabled(b_is_software_trigger and result3)
        ui.checkBox_software_trigger.setEnabled(b_is_trigger)
        ui.checkBox_hardware_trigger.setEnabled(b_is_trigger)

    def open_devices():
        global deviceList, obj_cam_operation, b_is_open, valid_number, cam_checked_list

        if b_is_open:
            return

        if len(cam_checked_list) == 0:
            print_text("please select a camera !")
            return

        obj_cam_operation = []
        b_checked = False
        for i in range(4):
            if i < len(cam_checked_list) and cam_checked_list[i]:
                b_checked = True
                camObj = MvCamera()
                cam_op = CameraOperation(camObj, deviceList, i)
                ret = cam_op.open_device()
                if ret != 0:
                    print_text("open cam %d fail ret[0x%x]" % (i, ret))
                    obj_cam_operation.append(0)
                    continue
                # 打开成功后，默认先配置成连续模式
                cam_op.set_continuous_mode()
                obj_cam_operation.append(cam_op)
                b_is_open = True
            else:
                obj_cam_operation.append(0)

        if not b_checked:
            print_text("please select a camera !")
            return
        if not b_is_open:
            print_text("no camera opened successfully !")
            return

        ui.radioButton_continuous.setChecked(True)
        enable_ui_controls()

        # 禁用已选相机按钮，避免重复打开
        for i in range(valid_number):
            cam_button_group.button(i).setEnabled(False)


    def software_trigger_check_box_clicked():
        """软件触发复选框点击事件 - 参考MultipleCameras.py的成功实现"""
        global obj_cam_operation, b_is_software_trigger, b_is_hardware_trigger, b_is_grab

        if ui.checkBox_software_trigger.isChecked():
            b_is_software_trigger = True
            b_is_hardware_trigger = False
            # 防止触发硬件触发复选框的clicked信号
            ui.checkBox_hardware_trigger.blockSignals(True)
            ui.checkBox_hardware_trigger.setChecked(False)
            ui.checkBox_hardware_trigger.blockSignals(False)

            # 先停采集再改模式，避免参数不生效
            was_grabbing = b_is_grab
            if b_is_grab:
                stop_grabbing()

            # 应用软件触发模式到所有相机
            for i in range(0, 4):
                if i < len(obj_cam_operation) and obj_cam_operation[i] != 0:
                    ret = obj_cam_operation[i].set_software_trigger()
                    if ret != 0:
                        print_text(f'相机{i} 软件触发模式设置失败 ret = {ToHexStr(ret)}')
                    else:
                        print_text(f'相机{i} 软件触发模式设置成功')

            # 如果之前在采集，重新启动采集
            if was_grabbing:
                start_grabbing()
        else:
            b_is_software_trigger = False
            print_text("切换到连续采集模式...")

            # 先停采集再改模式，避免参数不生效
            was_grabbing = b_is_grab
            if b_is_grab:
                stop_grabbing()

            # 应用连续模式到所有相机
            for i in range(0, 4):
                if i < len(obj_cam_operation) and obj_cam_operation[i] != 0:
                    ret = obj_cam_operation[i].set_continuous_mode()
                    if ret != 0:
                        print_text(f'相机{i} 连续模式设置失败 ret = {ToHexStr(ret)}')
                    else:
                        print_text(f'相机{i} 连续模式设置成功')

            # 如果之前在采集，重新启动采集
            if was_grabbing:
                start_grabbing()

        enable_ui_controls()

    def hardware_trigger_check_box_clicked():
        """硬件触发复选框点击事件 - 参考MultipleCameras.py的成功实现"""
        global obj_cam_operation, b_is_hardware_trigger, b_is_software_trigger, b_is_grab
        global hardware_trigger_line, hardware_trigger_activation

        if ui.checkBox_hardware_trigger.isChecked():
            b_is_hardware_trigger = True
            b_is_software_trigger = False
            # 防止触发软件触发复选框的clicked信号
            ui.checkBox_software_trigger.blockSignals(True)
            ui.checkBox_software_trigger.setChecked(False)
            ui.checkBox_software_trigger.blockSignals(False)

            # 先停采集再改模式，避免参数不生效
            was_grabbing = b_is_grab
            if b_is_grab:
                stop_grabbing()

            for i in range(0, 4):
                if i < len(obj_cam_operation) and obj_cam_operation[i] != 0:
                    # 基础硬件触发模式（On + LineX + RisingEdge）
                    ret = obj_cam_operation[i].set_hardware_trigger()
                    if ret != 0:
                        print_text('camera' + str(i) + ' set to hardware trigger fail! ret = ' + ToHexStr(ret))
                        continue
                    # 根据UI参数细化触发线与极性
                    try:
                        obj_cam_operation[i].obj_cam.MV_CC_SetEnumValueByString("TriggerSource", hardware_trigger_line)
                        obj_cam_operation[i].obj_cam.MV_CC_SetEnumValueByString("TriggerActivation", hardware_trigger_activation)
                    except Exception as e:
                        print_text('camera' + str(i) + f' apply trigger options fail: {e}')
                        continue
                    print_text('camera' + str(i) + f' set to hardware trigger mode ({hardware_trigger_line}, {hardware_trigger_activation})')

            # 如果之前在采集，重新启动采集
            if was_grabbing:
                start_grabbing()
        else:
            b_is_hardware_trigger = False

        enable_ui_controls()

    def radio_button_clicked(button):
        global obj_cam_operation, b_is_trigger

        button_id = raio_button_group.id(button)
        b_is_trigger = (button_id == 1)  # 1 = trigger on

        for i in range(4):
            if i < len(obj_cam_operation) and obj_cam_operation[i] != 0:
                if b_is_trigger:
                    if b_is_hardware_trigger:
                        obj_cam_operation[i].set_hardware_trigger()
                    elif b_is_software_trigger:
                        obj_cam_operation[i].set_software_trigger()
                    else:
                        obj_cam_operation[i].set_continuous_mode()
                else:
                    obj_cam_operation[i].set_continuous_mode()

        enable_ui_controls()

# {{ AURA-X: Delete - 删除重复的硬件触发设置函数. Approval: 寸止(ID:1678886400). }}

    def close_devices():
        global b_is_open
        global obj_cam_operation
        global valid_number

        if b_is_open is False:
            return
        if b_is_grab is True:
            stop_grabbing()
        for i in range(0, 4):
            if i < len(obj_cam_operation) and obj_cam_operation[i] != 0:
                ret = obj_cam_operation[i].close_device()
                if 0 != ret:
                    print_text('camera' + str(i) + ' close device fail! ret = ' + ToHexStr(ret))

            if i < valid_number:
                button_by_id = cam_button_group.button(i)
                button_by_id.setEnabled(True)
        b_is_open = False
        enable_ui_controls()

    def start_grabbing():
        global obj_cam_operation, win_display_handles, b_is_open, b_is_grab
        global b_is_software_trigger, b_is_hardware_trigger

        if (not b_is_open) or (b_is_grab is True):
            return

        # 启动采集 - 参考MultipleCameras.py的成功实现
        for i in range(0, 4):
            if i < len(obj_cam_operation) and obj_cam_operation[i] != 0:
                ret = obj_cam_operation[i].start_grabbing(i, win_display_handles[i])
                if ret != 0:
                    print_text(f'相机{i} 启动采集失败 ret = {ToHexStr(ret)}')
                else:
                    # A. 软件触发模式下，延迟打印一次Trigger配置（只读校验）
                    if b_is_software_trigger:
                        try:
                            QTimer.singleShot(200, lambda cam=i: print_text(f"cam{cam} started in SW trigger mode"))
                        except:
                            pass
                    elif b_is_hardware_trigger:
                        print_text(f'相机{i} 硬件触发采集已启动，等待外部触发信号')
                    else:
                        print_text(f'相机{i} 连续采集已启动')

        b_is_grab = True
        enable_ui_controls()

        # B. 若当前为硬件触发模式，则启动轮询自动保存
        if b_is_hardware_trigger:
            QTimer.singleShot(100, start_hw_trigger_polling)

        # 自动启动立体视觉（如果有两个或以上相机）
        selected_count = sum(1 for i in range(4) if i < len(obj_cam_operation) and obj_cam_operation[i] != 0)
        if selected_count >= 2:
            print_text("检测到多个相机，可以启动立体视觉")
            start_stereo_vision()

    def stop_grabbing():
        global b_is_grab
        global obj_cam_operation
        global b_is_open

        if (not b_is_open) or (b_is_grab is False):
            return

        # 停止硬件触发轮询
        stop_hw_trigger_polling()

        # 停止立体视觉
        stop_stereo_vision()

        for i in range(0, 4):
            if i < len(obj_cam_operation) and obj_cam_operation[i] != 0:
                ret = obj_cam_operation[i].stop_grabbing()
                if 0 != ret:
                    print_text('camera' + str(i) + ' stop grabbing fail!ret = ' + ToHexStr(ret))
                b_is_grab = False
        enable_ui_controls()

    # ch:存图 | en:save image - 增强版本支持触发图像保存
    def save_bmp():
        global b_is_grab
        global obj_cam_operation
        global last_trigger_images

        # 优先保存触发图像，如果没有则保存当前图像
        if (last_trigger_images["left"] is not None or
            last_trigger_images["right"] is not None):
            print_text("保存触发后的图像...")
            save_trigger_images_manual()
            return

        if b_is_grab is False:
            print_text("无法保存图像：相机未在采集状态")
            return

        try:
            # 创建保存目录
            save_dir = "saved_images"
            if not os.path.exists(save_dir):
                os.makedirs(save_dir)

            timestamp = int(time.time() * 1000)
            saved_count = 0

            for i in range(0, 4):
                if i < len(obj_cam_operation) and obj_cam_operation[i] != 0:
                    # 检查图像缓冲区是否有有效的图像数据
                    if (obj_cam_operation[i].buf_save_image is None or
                        obj_cam_operation[i].st_frame_info is None):
                        print_text(f'camera{i} has no image data to save.')
                        continue

                    frame_info = obj_cam_operation[i].st_frame_info

                    # 检查图像尺寸是否有效
                    if frame_info.nHeight <= 0 or frame_info.nWidth <= 0:
                        print_text(f'camera{i} frame info has invalid dimensions')
                        continue

                    try:
                        # 使用改进的保存方法，传递保存目录
                        ret = obj_cam_operation[i].save_bmp(save_dir)
                        if 0 == ret:
                            saved_count += 1
                            print_text(f'camera{i} image saved successfully to {save_dir}')
                        elif ret != -1:  # -1 表示没有图像数据，不是真正的错误
                            print_text('camera' + str(i) + ' save bmp fail!ret = ' + ToHexStr(ret))

                    except Exception as e:
                        print_text(f'camera{i} save error: {e}')
                        continue

            if saved_count > 0:
                print_text(f"成功保存 {saved_count} 张图像")
            else:
                print_text("未能保存任何图像")

        except Exception as e:
            print_text(f"保存图像错误: {e}")

    def save_trigger_images_manual():
        """手动保存触发后的图像"""
        global last_trigger_images

        try:
            save_dir = "trigger_images"
            if not os.path.exists(save_dir):
                os.makedirs(save_dir)

            timestamp = int(time.time() * 1000)
            saved_count = 0

            if last_trigger_images["left"] is not None:
                filename = f"trigger_left_{timestamp}.png"
                filepath = os.path.join(save_dir, filename)
                cv2.imwrite(filepath, last_trigger_images["left"])
                saved_count += 1
                print_text(f'Left trigger image saved: {filename}')

            if last_trigger_images["right"] is not None:
                filename = f"trigger_right_{timestamp}.png"
                filepath = os.path.join(save_dir, filename)
                cv2.imwrite(filepath, last_trigger_images["right"])
                saved_count += 1
                print_text(f'Right trigger image saved: {filename}')

            if saved_count > 0:
                print_text(f"成功保存 {saved_count} 张触发图像到 {save_dir}")
                # 清空触发图像缓存
                last_trigger_images = {"left": None, "right": None}
            else:
                print_text("没有触发图像可保存")

        except Exception as e:
            print_text(f"保存触发图像错误: {e}")

    def is_float(str_value):
        try:
            float(str_value)
            return True
        except ValueError:
            return False

    def set_parameters():
        global obj_cam_operation
        global b_is_open
        if b_is_open is False:
            return

        frame_rate = ui.lineEdit_frameRate.text()
        exposure_time = ui.lineEdit_exposureTime.text()
        gain = ui.lineEdit_gain.text()

        if is_float(frame_rate) is False or is_float(exposure_time) is False or is_float(gain) is False:
            print_text("parameters is valid, please check")
            return

        for i in range(0, 4):
            if obj_cam_operation[i] != 0:
                ret = obj_cam_operation[i].set_exposure_time(exposure_time)
                if ret != 0:
                    print_text('camera' + str(i) + ' Set exposure time failed ret:' + ToHexStr(ret))
                ret = obj_cam_operation[i].set_gain(gain)
                if ret != 0:
                    print_text('camera' + str(i) + ' Set gain failed ret:' + ToHexStr(ret))
                ret = obj_cam_operation[i].set_frame_rate(frame_rate)
                if ret != 0:
                    print_text('camera' + str(i) + ' set acquisition frame rate failed ret:' + ToHexStr(ret))


    def reset_trigger_cooldown():
        global _trigger_cooldown
        _trigger_cooldown = False
        print_text("Trigger cooldown reset")

    # 添加时间戳变量用于防抖
    _last_sw_trigger_time = 0

    def software_trigger_once():
        """执行一次软件触发 - 参考MultipleCameras.py的简洁实现"""
        global _last_sw_trigger_time, _trigger_processed
        current_time = time.time()

        # 防抖：1秒内只允许一次软件触发
        if current_time - _last_sw_trigger_time < 1.0:
            print_text("软件触发冷却中，请稍后再试")
            return

        _last_sw_trigger_time = current_time

        # 仅对0/1（左右目）进行一次软件触发，并在500ms后做一次立体匹配与保存
        triggered = 0
        for i in range(0, 2):
            if i < len(obj_cam_operation) and obj_cam_operation[i] != 0:
                ret = obj_cam_operation[i].trigger_once()
                if ret != 0:
                    print_text('camera' + str(i) + ' TriggerSoftware failed ret:' + ToHexStr(ret))
                else:
                    triggered += 1
                    print_text('camera' + str(i) + ' software trigger executed')
        if triggered > 0:
            _trigger_processed = True  # 设置触发标志
            QTimer.singleShot(500, attempt_stereo_after_sw_trigger)

    def attempt_stereo_after_sw_trigger():
        """软件触发后尝试立体匹配"""
        try:
            process_single_stereo_pair()
        except Exception as e:
            print_text(f"软件触发后立体匹配失败: {e}")

    # 设置硬件触发线
    def set_hardware_trigger_line(line_name):
        global hardware_trigger_line
        global obj_cam_operation
        global b_is_open
        global b_is_software_trigger

        if not b_is_open or b_is_software_trigger:
            hardware_trigger_line = line_name
            print_text(f'Hardware trigger line set to: {line_name}')
            return

        hardware_trigger_line = line_name
        # 如果当前是硬件触发模式，立即应用设置
        for i in range(0, 4):
            if i < len(obj_cam_operation) and obj_cam_operation[i] != 0:
                try:
                    obj_cam_operation[i].obj_cam.MV_CC_SetEnumValueByString("TriggerSource", hardware_trigger_line)
                    print_text('camera' + str(i) + f' trigger line set to: {line_name}')
                except Exception as e:
                    print_text('camera' + str(i) + f' set trigger line to {line_name} fail: {e}')

    # 设置硬件触发极性
    def set_hardware_trigger_activation(activation):
        global hardware_trigger_activation
        global obj_cam_operation
        global b_is_open
        global b_is_software_trigger

        if not b_is_open or b_is_software_trigger:
            hardware_trigger_activation = activation
            print_text(f'Hardware trigger activation set to: {activation}')
            return

        hardware_trigger_activation = activation
        # 如果当前是硬件触发模式，立即应用设置
        for i in range(0, 4):
            if i < len(obj_cam_operation) and obj_cam_operation[i] != 0:
                try:
                    obj_cam_operation[i].obj_cam.MV_CC_SetEnumValueByString("TriggerActivation", hardware_trigger_activation)
                    print_text('camera' + str(i) + f' trigger activation set to: {activation}')
                except Exception as e:
                    print_text('camera' + str(i) + f' set trigger activation to {activation} fail: {e}')

    # 硬件触发轮询相关变量和函数
    _hw_trigger_polling = False
    _last_hw_trigger_time = 0

    def start_hw_trigger_polling():
        global _hw_trigger_polling
        _hw_trigger_polling = True
        poll_hardware_trigger_results()

    def stop_hw_trigger_polling():
        global _hw_trigger_polling
        _hw_trigger_polling = False

    def poll_hardware_trigger_results():
        global _hw_trigger_polling, b_is_hardware_trigger, b_is_grab, _last_hw_trigger_time
        if not (_hw_trigger_polling and b_is_hardware_trigger and b_is_grab):
            return  # 停止轮询

        current_time = time.time()
        left = get_camera_image(0)
        right = get_camera_image(1)
        if (left is not None and right is not None and
            current_time - _last_hw_trigger_time > 1.0):  # 1秒防抖
            _last_hw_trigger_time = current_time
            process_single_stereo_pair()
        # 继续轮询
        QTimer.singleShot(100, poll_hardware_trigger_results)



    def process_single_stereo_pair_with_validation(pre_trigger_frames):
        """带验证的单次立体匹配处理"""
        global last_trigger_images, stereo_processor, obj_cam_operation

        print_text("开始验证触发后的图像...")

        # 验证是否获取到新图像
        max_wait_attempts = 10
        wait_count = 0
        new_images_ready = False

        while wait_count < max_wait_attempts and not new_images_ready:
            new_images_ready = True
            for i in range(2):  # 检查左右相机
                if i < len(obj_cam_operation) and obj_cam_operation[i] != 0:
                    current_frame = getattr(obj_cam_operation[i], 'frame_count', 0)
                    pre_frame = pre_trigger_frames.get(i, 0)
                    if current_frame <= pre_frame:
                        new_images_ready = False
                        break

            if not new_images_ready:
                time.sleep(0.05)  # 等待50ms
                wait_count += 1

        if not new_images_ready:
            print_text("等待新图像超时，使用当前可用图像")

        # 获取左右图像
        left_image = get_camera_image(0)
        right_image = get_camera_image(1)

        if left_image is None or right_image is None:
            print_text("未能获取完整的立体图像对")
            return

        # 保存用于调试
        last_trigger_images["left"] = left_image
        last_trigger_images["right"] = right_image

        # 初始化立体视觉（如果未初始化）
        if stereo_processor is None:
            if not init_stereo_vision():
                return

        # 执行一次立体匹配
        stereo_processor.process_stereo_pair(left_image, right_image)

        print_text("带验证的单次立体匹配完成")

    # {{ AURA-X: Add - 添加硬件触发处理函数. Approval: 寸止(ID:1678886400). }}
    def hardware_trigger_once():
        """硬件触发处理函数"""
        print("[DEBUG] hardware_trigger_once() 被调用了")

        global obj_cam_operation, _trigger_processed, _trigger_cooldown

        if _trigger_processed or _trigger_cooldown:
            print_text("硬件触发冷却中，跳过本次触发")
            return

        _trigger_processed = True
        _trigger_cooldown = True

        print_text("收到硬件触发信号")

        # 硬件触发模式下，相机会自动采集图像，无需手动触发
        # 等待图像采集完成后处理立体匹配（统一 500ms 延迟）
        QTimer.singleShot(500, process_single_stereo_pair)

        # 冷却定时器
        QTimer.singleShot(int(_trigger_cooldown_time * 1000), reset_trigger_cooldown)

    #处理单次立体匹配
    def process_single_stereo_pair():
        global last_trigger_images, stereo_processor, _trigger_processed, _trigger_cooldown

        if not _trigger_processed:
            print_text("Trigger not initiated, skipping...")
            return

        # 获取左右图像
        left_image = get_camera_image(0)
        right_image = get_camera_image(1)

        if left_image is None or right_image is None:
            print_text("未能获取完整的立体图像对")
            _trigger_processed = False
            return

        # 保存用于调试
        last_trigger_images["left"] = left_image
        last_trigger_images["right"] = right_image

        # 初始化立体视觉（如果未初始化）
        if stereo_processor is None:
            if not init_stereo_vision():
                _trigger_processed = False
                return

        # 执行一次立体匹配
        stereo_processor.process_stereo_pair(left_image, right_image)

        # ✅ 强制保存点云和结果
        save_stereo_results(left_image, right_image, {
            'depth': stereo_processor.latest_depth,
            # 'points_3d': stereo_processor.latest_points_3d
        })

        print_text("单次立体匹配完成，点云已保存")
        _trigger_processed = False
        # 不要重置 _TRIGGER_COOL，让冷却定时器自然结束

    def capture_and_save_trigger_images():
        """捕获触发后的图像并自动保存"""
        global last_trigger_images, obj_cam_operation

        try:
            print_text("正在捕获触发后的图像...")

            # 创建保存目录
            save_dir = "trigger_images"
            if not os.path.exists(save_dir):
                os.makedirs(save_dir)

            timestamp = int(time.time() * 1000)
            saved_count = 0

            # 获取并保存所有相机的图像
            for i in range(0, 4):
                if i < len(obj_cam_operation) and obj_cam_operation[i] != 0:
                    # 检查图像缓冲区是否有有效的图像数据
                    if (obj_cam_operation[i].buf_save_image is None or
                        obj_cam_operation[i].st_frame_info is None):
                        print_text(f'camera{i} has no image data to save.')
                        continue

                    frame_info = obj_cam_operation[i].st_frame_info

                    # 检查图像尺寸是否有效
                    if frame_info.nHeight <= 0 or frame_info.nWidth <= 0:
                        print_text(f'camera{i} frame info has invalid dimensions')
                        continue

                    try:
                        # 获取图像数据
                        image_data = np.frombuffer(
                            obj_cam_operation[i].buf_save_image,
                            dtype=np.uint8
                        )

                        # 根据像素格式重塑图像
                        if len(image_data) >= frame_info.nHeight * frame_info.nWidth:
                            # 简单处理为灰度图像
                            image = image_data[:frame_info.nHeight * frame_info.nWidth].reshape(
                                (frame_info.nHeight, frame_info.nWidth)
                            )

                            # 保存图像
                            filename = f"trigger_cam{i}_{timestamp}.png"
                            filepath = os.path.join(save_dir, filename)
                            cv2.imwrite(filepath, image)

                            saved_count += 1
                            print_text(f'camera{i} image saved: {filename}')

                            # 如果是前两个相机，保存到立体匹配用的变量中
                            if i == 0:
                                last_trigger_images["left"] = cv2.cvtColor(image, cv2.COLOR_GRAY2BGR)
                            elif i == 1:
                                last_trigger_images["right"] = cv2.cvtColor(image, cv2.COLOR_GRAY2BGR)

                    except Exception as e:
                        print_text(f'camera{i} save error: {e}')
                        continue

            if saved_count > 0:
                print_text(f"触发图像保存完成！共保存 {saved_count} 张图像到 {save_dir}")

                # 检查是否成功获取了双目图像，如果是则可以进行立体匹配
                if last_trigger_images["left"] is not None and last_trigger_images["right"] is not None:
                    print_text("双目图像捕获成功，可以进行立体匹配")
                    # 如果启用了自动立体匹配，则自动进行立体匹配
                    if trigger_stereo_match:
                        perform_trigger_stereo_match()
            else:
                print_text("未能保存任何触发图像")

        except Exception as e:
            print_text(f"触发图像捕获错误: {e}")

    def capture_trigger_images():
        print("[DEBUG] capture_trigger_images() 被调用了")  # ✅ 加这一行
        """捕获触发后的图像"""
        global last_trigger_images, obj_cam_operation

        try:
            # 获取左相机图像（相机0）
            left_image = get_camera_image(0)
            if left_image is not None:
                last_trigger_images["left"] = left_image.copy()
                print_text("Left camera image captured")

            # 获取右相机图像（相机1）
            right_image = get_camera_image(1)
            if right_image is not None:
                last_trigger_images["right"] = right_image.copy()
                print_text("Right camera image captured")

            # 检查是否成功获取了双目图像
            if last_trigger_images["left"] is not None and last_trigger_images["right"] is not None:
                print_text("Stereo image pair captured successfully")
                # 如果启用了自动立体匹配，则自动进行立体匹配
                if trigger_stereo_match:
                    perform_trigger_stereo_match()
            else:
                print_text("Failed to capture stereo image pair")

        except Exception as e:
            print_text(f"Error capturing trigger images: {e}")

    def perform_trigger_stereo_match():
        print("[DEBUG] perform_trigger_stereo_match() 被调用了")
        """对触发后的图像进行立体匹配"""
        global last_trigger_images, stereo_processor

        if (last_trigger_images["left"] is None or
            last_trigger_images["right"] is None):
            print_text("No captured images available for stereo matching")
            return

        try:
            if stereo_processor is None:
                if not init_stereo_vision():
                    return

            print_text("Processing stereo matching...")

            # 处理立体图像对
            result = stereo_processor.process_stereo_pair(
                last_trigger_images["left"],
                last_trigger_images["right"]
            )

            # 保存结果
            save_stereo_results(last_trigger_images["left"], last_trigger_images["right"], result)

            print_text("Stereo matching completed")

        except Exception as e:
            print_text(f"Error in stereo matching: {e}")

    def save_stereo_results(left_img, right_img, result):
        """保存立体匹配结果（原图/视差/深度/点云）"""
        try:
            root_dir = "stereo_results"
            os.makedirs(root_dir, exist_ok=True)
            timestamp = int(time.time() * 1000)
            save_dir = os.path.join(root_dir, str(timestamp))
            os.makedirs(save_dir, exist_ok=True)

            # 1) 保存原始左右图
            if left_img is not None:
                cv2.imwrite(os.path.join(save_dir, "left.png"), left_img)
            if right_img is not None:
                cv2.imwrite(os.path.join(save_dir, "right.png"), right_img)

            # 2) 保存视差与伪彩
            if stereo_processor is not None and getattr(stereo_processor, 'latest_disparity', None) is not None:
                disp = stereo_processor.latest_disparity
                disp_norm = cv2.normalize(disp, None, 0, 255, cv2.NORM_MINMAX)
                disp_color = cv2.applyColorMap(disp_norm.astype(np.uint8), cv2.COLORMAP_JET)
                cv2.imwrite(os.path.join(save_dir, "disparity_color.png"), disp_color)

            # 3) 保存深度（已有）
            if result and 'depth' in result and result['depth'] is not None:
                depth_norm = cv2.normalize(result['depth'], None, 0, 255, cv2.NORM_MINMAX)
                depth_color = cv2.applyColorMap(depth_norm.astype(np.uint8), cv2.COLORMAP_JET)
                cv2.imwrite(os.path.join(save_dir, "depth.png"), depth_color)

            # 4) 保存点云（若存在）
            if stereo_processor is not None and getattr(stereo_processor, 'latest_points_3d', None) is not None:
                points = stereo_processor.latest_points_3d
                if points is not None and left_img is not None:
                    mask = (points[:, :, 2] >= 1500) & (points[:, :, 2] <= 8000)
                    valid_points = points[mask]
                    valid_colors = cv2.cvtColor(left_img, cv2.COLOR_BGR2RGB)[mask]
                    if len(valid_points) > 0:
                        stereo_processor._save_ply(valid_points, valid_colors, save_dir, timestamp)

            print_text(f"Stereo results saved to {save_dir}")
        except Exception as e:
            print_text(f"Error saving stereo results: {e}")

    def manual_stereo_match():
        """手动触发立体匹配"""
        global trigger_stereo_match

        if (last_trigger_images["left"] is None or
            last_trigger_images["right"] is None):
            print_text("No captured images available. Please trigger cameras first.")
            return

        print_text("Manual stereo matching triggered...")
        perform_trigger_stereo_match()

    def enable_auto_stereo_match():
        """启用自动立体匹配"""
        global trigger_stereo_match
        trigger_stereo_match = True
        print_text("自动立体匹配已启用")

    def disable_auto_stereo_match():
        """禁用自动立体匹配"""
        global trigger_stereo_match
        trigger_stereo_match = False
        print_text("自动立体匹配已禁用")

    def set_hardware_trigger_line(self, line_name):
        if self.cam is None:
            return

        try:
            ret = self.cam.MV_CC_SetEnumValue("TriggerSource", line_name)
            if ret != 0:
                print(f"设置触发线失败! ret[0x{ret:x}]")
                return

            print(f"硬件触发线设置为: {line_name}")
        except Exception as e:
            print(f"设置硬件触发线时出错: {e}")


    def set_hardware_trigger_activation(self, activation):
        if self.cam is None:
            return

        try:
            ret = self.cam.MV_CC_SetEnumValue("TriggerActivation", activation)
            if ret != 0:
                print(f"设置触发极性失败! ret[0x{ret:x}]")
                return

            print(f"硬件触发极性设置为: {activation}")
        except Exception as e:
            print(f"设置硬件触发极性时出错: {e}")

    # ch: 初始化app, 绑定控件与函数 | en: Init app, bind ui and api
    app = QApplication(sys.argv)
    mainWindow = QMainWindow()
    ui = Ui_MainWindow()
    ui.setupUi(mainWindow)
    ui.pushButton_enum.clicked.connect(enum_devices)
    ui.pushButton_open.clicked.connect(open_devices)
    ui.pushButton_close.clicked.connect(close_devices)
    ui.pushButton_startGrab.clicked.connect(start_grabbing)
    ui.pushButton_stopGrab.clicked.connect(stop_grabbing)
    ui.pushButton_saveImg.clicked.connect(save_bmp)
    ui.pushButton_setParams.clicked.connect(set_parameters)
    ui.checkBox_software_trigger.clicked.connect(software_trigger_check_box_clicked)
    ui.checkBox_hardware_trigger.clicked.connect(hardware_trigger_check_box_clicked)
    ui.pushButton_triggerOnce.clicked.connect(software_trigger_once)

    # 绑定立体匹配按钮（如果UI中有的话）
    if hasattr(ui, 'pushButton_stereo_match'):
        ui.pushButton_stereo_match.clicked.connect(manual_stereo_match)
    cam_button_group = QButtonGroup(mainWindow)
    cam_button_group.addButton(ui.checkBox_1, 0)
    cam_button_group.addButton(ui.checkBox_2, 1)
    cam_button_group.addButton(ui.checkBox_3, 2)
    cam_button_group.addButton(ui.checkBox_4, 3)

    cam_button_group.setExclusive(False)
    cam_button_group.buttonClicked.connect(cam_check_box_clicked)

    raio_button_group = QButtonGroup(mainWindow)
    raio_button_group.addButton(ui.radioButton_continuous, 0)
    raio_button_group.addButton(ui.radioButton_trigger, 1)
    raio_button_group.buttonClicked.connect(radio_button_clicked)

    win_display_handles.append(ui.widget_display1.winId())
    win_display_handles.append(ui.widget_display2.winId())
    win_display_handles.append(ui.widget_display3.winId())
    win_display_handles.append(ui.widget_display4.winId())

    mainWindow.show()
    enum_devices()
    enable_ui_controls()

    # 初始化立体视觉
    init_stereo_vision()

    app.exec_()

    close_devices()

    # ch:反初始化SDK | en: finalize SDK
    MvCamera.MV_CC_Finalize()

    sys.exit()